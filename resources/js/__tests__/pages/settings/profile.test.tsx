import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Profile from '@/pages/settings/profile';
import { type User } from '@/types';
import { vi } from 'vitest';

// Mock Inertia components and hooks
const mockPost = vi.fn();
const mockUsePage = vi.fn();

// Mock global route function
global.route = vi.fn((name: string) => `/mock-route/${name}`);

vi.mock('@inertiajs/react', () => {
    const mockPost = vi.fn();
    return {
    Head: ({ children }: { children: React.ReactNode }) => <head>{children}</head>,
    Link: ({ children, href, ...props }: Record<string, unknown>) => (
        <a href={href as string} {...props}>
            {children}
        </a>
    ),
    Form: ({ children, ...props }: Record<string, unknown>) => {
        const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();
            const formData = new FormData(e.target as HTMLFormElement);
            const data = Object.fromEntries(formData.entries());
            mockPost(props.action, data);
        };
        return <form onSubmit={handleSubmit} {...props}>{children({ processing: false, recentlySuccessful: false, errors: {} })}</form>;
    },
    usePage: () => mockUsePage(),
    router: {
        post: mockPost,
    },
}});

// Mock layout components
vi.mock('@/layouts/app-layout', () => {
    return {
        default: function AppLayout({ children }: { children: React.ReactNode }) {
            return <div data-testid="app-layout">{children}</div>;
        }
    };
});

vi.mock('@/layouts/settings/layout', () => {
    return {
        default: function SettingsLayout({ children }: { children: React.ReactNode }) {
            return <div data-testid="settings-layout">{children}</div>;
        }
    };
});

vi.mock('@/components/delete-user', () => {
    return {
        default: function DeleteUser() {
            return <div data-testid="delete-user">Delete User Component</div>;
        }
    };
});

const mockUser: User = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'client',
    phone: '+1234567890',
    company: 'Test Company',
    avatar: 'https://example.com/avatar.jpg',
    email_verified_at: '2024-01-01T00:00:00Z',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
};

describe('Profile Page', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockUser }
            }
        });
    });

    it('renders profile page correctly', () => {
        render(<Profile mustVerifyEmail={false} />);

        expect(screen.getByTestId('app-layout')).toBeInTheDocument();
        expect(screen.getByTestId('settings-layout')).toBeInTheDocument();
        expect(screen.getByText('Profile Information')).toBeInTheDocument();
        expect(screen.getByText('Personal Details')).toBeInTheDocument();
        expect(screen.getByText('Account Security')).toBeInTheDocument();
    });

    it('displays user information correctly', () => {
        render(<Profile mustVerifyEmail={false} />);

        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('Client Account')).toBeInTheDocument();
    });

    it('displays user avatar with fallback initials', () => {
        render(<Profile mustVerifyEmail={false} />);

        const avatar = screen.getByRole('img');
        expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');
        expect(avatar).toHaveAttribute('alt', 'John Doe');
    });

    it('shows initials when no avatar is provided', () => {
        const userWithoutAvatar = { ...mockUser, avatar: undefined };
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: userWithoutAvatar }
            }
        });

        render(<Profile mustVerifyEmail={false} />);

        expect(screen.getByText('JD')).toBeInTheDocument(); // Initials for John Doe
    });

    it('pre-fills form fields with user data', () => {
        render(<Profile mustVerifyEmail={false} />);

        const nameInput = screen.getByDisplayValue('John Doe');
        const emailInput = screen.getByDisplayValue('<EMAIL>');
        const phoneInput = screen.getByDisplayValue('+1234567890');
        const companyInput = screen.getByDisplayValue('Test Company');

        expect(nameInput).toBeInTheDocument();
        expect(emailInput).toBeInTheDocument();
        expect(phoneInput).toBeInTheDocument();
        expect(companyInput).toBeInTheDocument();
    });

    it('handles form submission correctly', async () => {
        const user = userEvent.setup();
        render(<Profile mustVerifyEmail={false} />);

        const nameInput = screen.getByDisplayValue('John Doe');
        const saveButton = screen.getByText('Save Changes');

        await user.clear(nameInput);
        await user.type(nameInput, 'Jane Doe');
        await user.click(saveButton);

        await waitFor(() => {
            expect(mockPost).toHaveBeenCalledWith(
                expect.stringContaining('/settings/profile'),
                expect.objectContaining({
                    name: 'Jane Doe',
                    email: '<EMAIL>',
                    phone: '+1234567890',
                    company: 'Test Company',
                })
            );
        });
    });

    it('shows email verification warning when email is not verified', () => {
        const unverifiedUser = { ...mockUser, email_verified_at: null };
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: unverifiedUser }
            }
        });

        render(<Profile mustVerifyEmail={true} />);

        expect(screen.getByText(/Your email address is unverified/)).toBeInTheDocument();
        expect(screen.getByText(/Click here to resend the verification email/)).toBeInTheDocument();
    });

    it('does not show email verification warning when email is verified', () => {
        render(<Profile mustVerifyEmail={false} />);

        expect(screen.queryByText(/Your email address is unverified/)).not.toBeInTheDocument();
    });

    it('shows verification link sent message when status is provided', () => {
        const unverifiedUser = { ...mockUser, email_verified_at: null };
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: unverifiedUser }
            }
        });

        render(<Profile mustVerifyEmail={true} status="verification-link-sent" />);

        expect(screen.getByText(/A new verification link has been sent/)).toBeInTheDocument();
    });

    it('displays account security section correctly', () => {
        render(<Profile mustVerifyEmail={false} />);

        expect(screen.getByText('Account Security')).toBeInTheDocument();
        expect(screen.getByText('Password')).toBeInTheDocument();
        expect(screen.getByText('Email Verification')).toBeInTheDocument();
        expect(screen.getByText('Change Password')).toBeInTheDocument();
    });

    it('shows verified status for verified email', () => {
        render(<Profile mustVerifyEmail={false} />);

        expect(screen.getByText('✓ Verified')).toBeInTheDocument();
    });

    it('shows not verified status for unverified email', () => {
        const unverifiedUser = { ...mockUser, email_verified_at: null };
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: unverifiedUser }
            }
        });

        render(<Profile mustVerifyEmail={false} />);

        expect(screen.getByText('Not verified')).toBeInTheDocument();
        expect(screen.getByText('Verify Email')).toBeInTheDocument();
    });

    it('includes delete user component', () => {
        render(<Profile mustVerifyEmail={false} />);

        expect(screen.getByTestId('delete-user')).toBeInTheDocument();
    });

    it('handles empty optional fields correctly', () => {
        const userWithoutOptionalFields = {
            ...mockUser,
            phone: null,
            company: null,
            avatar: null,
        };
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: userWithoutOptionalFields }
            }
        });

        render(<Profile mustVerifyEmail={false} />);

        const phoneInput = screen.getByPlaceholderText('Enter your phone number');
        const companyInput = screen.getByPlaceholderText('Enter your company name');

        expect(phoneInput).toHaveValue('');
        expect(companyInput).toHaveValue('');
    });

    it('displays correct role formatting', () => {
        const teamMemberUser = { ...mockUser, role: 'team_member' };
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: teamMemberUser }
            }
        });

        render(<Profile mustVerifyEmail={false} />);

        expect(screen.getByText('Team Member Account')).toBeInTheDocument();
    });

    it('shows camera icon for avatar upload', () => {
        render(<Profile mustVerifyEmail={false} />);

        const cameraButton = screen.getByRole('button');
        expect(cameraButton).toBeInTheDocument();
        // Camera icon should be present (testing for the button that contains it)
    });

    it('has proper form labels and accessibility', () => {
        render(<Profile mustVerifyEmail={false} />);

        expect(screen.getByLabelText(/Full Name/)).toBeInTheDocument();
        expect(screen.getByLabelText(/Email Address/)).toBeInTheDocument();
        expect(screen.getByLabelText(/Phone Number/)).toBeInTheDocument();
        expect(screen.getByLabelText(/Company/)).toBeInTheDocument();
    });
});
